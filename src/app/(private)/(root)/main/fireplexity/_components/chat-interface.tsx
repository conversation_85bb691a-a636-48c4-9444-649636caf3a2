'use client'

import { useRef, useEffect } from 'react'
import { Send, Loader2, User, <PERSON><PERSON><PERSON>, FileText, Plus, Copy, Refresh<PERSON>w, Check } from 'lucide-react'
import { useState } from 'react'
import { But<PERSON> } from '@/components'
import { SearchResult } from '../_lib/types'
import { type Message } from 'ai'
import { CharacterCounter } from './character-counter'
import Image from 'next/image'
import { MarkdownRenderer } from './markdown-renderer'
import { StockChart } from './stock-chart'

interface MessageData {
  sources: SearchResult[]
  followUpQuestions: string[]
  ticker?: string
}

interface ChatInterfaceProps {
  messages: Message[]
  sources: SearchResult[]
  followUpQuestions: string[]
  searchStatus: string
  isLoading: boolean
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) => void
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void
  messageData?: Map<number, MessageData>
  currentTicker?: string | null
}

export function ChatInterface({ messages, sources, followUpQuestions, searchStatus, isLoading, input, handleInputChange, handleSubmit, messageData, currentTicker }: ChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const formRef = useRef<HTMLFormElement>(null)
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)
  
  // Simple theme detection based on document class
  const theme = typeof window !== 'undefined' && document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  
  // Extract the current query and check if we're waiting for response
  let query = ''
  let isWaitingForResponse = false
  
  if (messages.length > 0) {
    const lastMessage = messages[messages.length - 1]
    const secondLastMessage = messages[messages.length - 2]
    
    if (lastMessage.role === 'user') {
      // Waiting for response to this user message
      query = lastMessage.content
      isWaitingForResponse = true
    } else if (secondLastMessage?.role === 'user' && lastMessage.role === 'assistant') {
      // Current conversation pair
      query = secondLastMessage.content
      isWaitingForResponse = false
    }
  }

  const scrollContainerRef = useRef<HTMLDivElement>(null)
  
  // Auto-scroll to bottom when new content appears
  useEffect(() => {
    if (!scrollContainerRef.current) return
    
    const container = scrollContainerRef.current
    
    // Always scroll to bottom when new messages arrive
    setTimeout(() => {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      })
    }, 100)
  }, [messages, sources, followUpQuestions])

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return
    handleSubmit(e)
    
    // Scroll to bottom after submitting
    setTimeout(() => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTo({
          top: scrollContainerRef.current.scrollHeight,
          behavior: 'smooth'
        })
      }
    }, 100)
  }

  const handleFollowUpClick = (question: string) => {
    // Set the input and immediately submit
    handleInputChange({ target: { value: question } } as React.ChangeEvent<HTMLTextAreaElement>)
    // Submit the form after a brief delay to ensure input is set
    setTimeout(() => {
      formRef.current?.requestSubmit()
    }, 50)
  }

  const copyToClipboard = async (text: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  // Group messages into conversation pairs
  const conversationPairs = []
  for (let i = 0; i < messages.length; i += 2) {
    const user = messages[i]
    const assistant = messages[i + 1]
    if (user?.role === 'user') {
      conversationPairs.push({ user, assistant })
    }
  }

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Scrollable content area */}
      <div
        ref={scrollContainerRef}
        className="flex-1 overflow-y-auto px-4 sm:px-6 lg:px-8 pb-32"
      >
        {/* Conversation pairs */}
        {conversationPairs.map((pair, pairIndex) => {
          // Get message-specific data
          const messageIndex = pairIndex * 2 + 1 // Assistant message index
          const messageSources = messageData?.get(messageIndex)?.sources || []
          const messageFollowUpQuestions = messageData?.get(messageIndex)?.followUpQuestions || []
          const messageTicker = messageData?.get(messageIndex)?.ticker

          return (
            <div key={pairIndex} className="mb-8">
              {/* User message */}
              <div className="flex items-start gap-3 mb-6">
                <div className="flex-shrink-0 w-8 h-8 bg-gray-100 dark:bg-zinc-800 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-gray-900 dark:text-gray-100 leading-relaxed">
                    {pair.user.content}
                  </p>
                </div>
              </div>

              {/* Assistant response */}
              {pair.assistant && (
                <div className="ml-11">
                  {/* Sources - Show for each assistant response */}
                  {messageSources.length > 0 && (
                    <div className="opacity-0 animate-fade-up [animation-duration:300ms] [animation-fill-mode:forwards] mb-6">
                      <div className="flex items-center gap-2 mb-3">
                        <FileText className="h-4 w-4 text-blue-500" />
                        <h2 className="text-sm font-medium text-gray-600 dark:text-gray-400">Sources</h2>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        {messageSources.map((result, index) => (
                          <a
                            key={index}
                            href={result.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group block p-3 bg-white dark:bg-zinc-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 transition-colors"
                          >
                            <div className="flex items-start gap-2">
                              <div className="flex-shrink-0 mt-1">
                                {result.favicon ? (
                                  <Image
                                    src={result.favicon}
                                    alt=""
                                    width={16}
                                    height={16}
                                    className="w-4 h-4 object-contain"
                                    unoptimized
                                  />
                                ) : (
                                  <FileText className="h-4 w-4 text-gray-400" />
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                  {result.title}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                                  {new URL(result.url).hostname}
                                </p>

                                {/* Character count */}
                                <div className="mt-1">
                                  <CharacterCounter
                                    targetCount={result.markdown?.length || result.content?.length || 0}
                                    duration={2000}
                                  />
                                </div>
                              </div>
                            </div>
                          </a>
                        ))}
                      </div>
                    </div>
                  )}


                  {/* Stock Chart - Show if ticker is available */}
                  {messageTicker && (
                    <div className="mb-6">
                      <StockChart ticker={messageTicker} theme={theme} />
                    </div>
                  )}

                  {/* Assistant message with copy button */}
                  <div className="bg-gray-50 dark:bg-zinc-900 rounded-lg p-4 mb-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                          <Sparkles className="h-3 w-3 text-white" />
                        </div>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Assistant</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => copyToClipboard(pair.assistant?.content || '', pair.assistant?.id || '')}
                          className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors rounded"
                          title="Copy response"
                        >
                          {copiedMessageId === pair.assistant?.id ? (
                            <Check className="h-3.5 w-3.5" />
                          ) : (
                            <Copy className="h-3.5 w-3.5" />
                          )}
                        </button>
                      </div>
                    </div>
                    <div className="prose prose-gray max-w-none dark:prose-invert">
                      <MarkdownRenderer
                        content={pair.assistant?.content || ''}
                        sources={messageSources}
                      />
                    </div>
                  </div>

                  {/* Related Questions - Show after each assistant response */}
                  {messageFollowUpQuestions.length > 0 && (
                    <div className="opacity-0 animate-fade-up [animation-duration:300ms] [animation-fill-mode:forwards] mt-6">
                      <div className="flex items-center gap-2 mb-3">
                        <Sparkles className="h-4 w-4 text-red-500" />
                        <h2 className="text-sm font-medium text-gray-600 dark:text-gray-400">Related</h2>
                      </div>
                      <div className="space-y-2">
                        {messageFollowUpQuestions.map((question, qIndex) => (
                          <button
                            key={qIndex}
                            onClick={() => handleFollowUpClick(question)}
                            disabled={isLoading}
                            className="w-full text-left p-3 bg-white dark:bg-zinc-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-orange-300 dark:hover:border-orange-600 hover:bg-orange-50 dark:hover:bg-orange-950/20 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group"
                          >
                            <div className="flex items-center gap-2">
                              <Plus className="h-3 w-3 text-gray-400 group-hover:text-orange-500 transition-colors flex-shrink-0" />
                              <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">
                                {question}
                              </span>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )
        })}

        {/* Current search status and sources (for ongoing search) */}
        {isWaitingForResponse && (
          <div className="mb-8">
            {/* User message for current query */}
            <div className="flex items-start gap-3 mb-6">
              <div className="flex-shrink-0 w-8 h-8 bg-gray-100 dark:bg-zinc-800 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-gray-900 dark:text-gray-100 leading-relaxed">
                  {query}
                </p>
              </div>
            </div>

            <div className="ml-11">
              {/* Search status */}
              {searchStatus && (
                <div className="flex items-center gap-2 mb-4 text-sm text-gray-600 dark:text-gray-400">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>{searchStatus}</span>
                </div>
              )}

              {/* Current sources */}
              {sources.length > 0 && (
                <div className="opacity-0 animate-fade-up [animation-duration:300ms] [animation-fill-mode:forwards] mb-6">
                  <div className="flex items-center gap-2 mb-3">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <h2 className="text-sm font-medium text-gray-600 dark:text-gray-400">Sources</h2>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {sources.map((result, index) => (
                      <a
                        key={index}
                        href={result.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="group block p-3 bg-white dark:bg-zinc-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 transition-colors"
                      >
                        <div className="flex items-start gap-2">
                          <div className="flex-shrink-0 mt-1">
                            {result.favicon ? (
                              <Image
                                src={result.favicon}
                                alt=""
                                width={16}
                                height={16}
                                className="w-4 h-4 object-contain"
                                unoptimized
                              />
                            ) : (
                              <FileText className="h-4 w-4 text-gray-400" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                              {result.title}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                              {new URL(result.url).hostname}
                            </p>

                            {/* Character count */}
                            <div className="mt-1">
                              <CharacterCounter
                                targetCount={result.markdown?.length || result.content?.length || 0}
                                duration={2000}
                              />
                            </div>
                          </div>
                        </div>
                      </a>
                    ))}
                  </div>
                </div>
              )}


              {/* Stock Chart - Show if ticker is available */}
              {currentTicker && messages.length > 0 && messages[messages.length - 2]?.role === 'user' && (
                <div className="opacity-0 animate-fade-up [animation-duration:500ms] [animation-delay:200ms] [animation-fill-mode:forwards] mb-6">
                  <StockChart ticker={currentTicker} theme={theme} />
                </div>
              )}

              {/* Loading indicator for assistant response */}
              {isLoading && (
                <div className="bg-gray-50 dark:bg-zinc-900 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                      <Sparkles className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Assistant</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm">Thinking...</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Follow-up questions for the latest response */}
        {!isWaitingForResponse && followUpQuestions.length > 0 && (
          <div className="opacity-0 animate-fade-up [animation-duration:300ms] [animation-fill-mode:forwards] mb-6">
            <div className="flex items-center gap-2 mb-3">
              <Sparkles className="h-4 w-4 text-red-500" />
              <h2 className="text-sm font-medium text-gray-600 dark:text-gray-400">Related</h2>
            </div>
            <div className="space-y-2">
              {followUpQuestions.map((question, index) => (
                <button
                  key={index}
                  onClick={() => handleFollowUpClick(question)}
                  disabled={isLoading}
                  className="w-full text-left p-3 bg-white dark:bg-zinc-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-orange-300 dark:hover:border-orange-600 hover:bg-orange-50 dark:hover:bg-orange-950/20 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group"
                >
                  <div className="flex items-center gap-2">
                    <Plus className="h-3 w-3 text-gray-400 group-hover:text-orange-500 transition-colors flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">
                      {question}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Fixed input at bottom */}
      <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white/95 dark:from-zinc-900 dark:via-zinc-900/95 to-transparent pt-6 pb-6 z-10">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <form onSubmit={handleFormSubmit} ref={formRef}>
            <div className="bg-white dark:bg-zinc-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-3 focus-within:border-gray-900 dark:focus-within:border-gray-100 transition-colors">
              <div className="flex items-end gap-2">
                <textarea
                  value={input}
                  onChange={handleInputChange}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      formRef.current?.requestSubmit()
                    }
                  }}
                  placeholder="Ask a follow-up question..."
                  className="flex-1 resize-none bg-transparent border-0 outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 text-sm leading-6 max-h-32 min-h-[24px]"
                  rows={1}
                  disabled={isLoading}
                  style={{
                    height: 'auto',
                    minHeight: '24px',
                    maxHeight: '128px',
                  }}
                  onInput={(e) => {
                    const target = e.target as HTMLTextAreaElement
                    target.style.height = 'auto'
                    target.style.height = Math.min(target.scrollHeight, 128) + 'px'
                  }}
                />
                <Button
                  type="submit"
                  disabled={isLoading || !input.trim()}
                  size="sm"
                  className="flex-shrink-0 bg-orange-500 hover:bg-orange-600 text-white rounded-lg px-3 py-2"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
