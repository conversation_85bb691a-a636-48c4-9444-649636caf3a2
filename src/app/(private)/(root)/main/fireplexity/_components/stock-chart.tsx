'use client'

import React, { useEffect, useRef, memo, useState } from 'react'

interface TradingViewWidgetProps {
  symbol: string
  theme?: 'light' | 'dark'
}

function TradingViewWidget({ symbol, theme = 'light' }: TradingViewWidgetProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return
    const container = containerRef.current
    if (!container) return

    // Clear any existing content
    container.innerHTML = `
      <div class="tradingview-widget-container__widget" style="height: 100%; width: 100%;"></div>
      <div class="tradingview-widget-copyright">
        <a href="https://www.tradingview.com/" rel="noopener nofollow" target="_blank">
          <span class="blue-text">Track all markets on TradingView</span>
        </a>
      </div>
    `

    const script = document.createElement('script')
    script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js'
    script.type = 'text/javascript'
    script.async = true
    script.innerHTML = JSON.stringify({
      autosize: false,
      symbol: symbol,
      interval: 'D',
      timezone: 'Etc/UTC',
      theme: theme,
      style: '2',
      locale: 'en',
      allow_symbol_change: true,
      save_image: false,
      support_host: 'https://www.tradingview.com',
      width: '100%',
      height: 300
    })

    container.appendChild(script)

    // Cleanup
    return () => {
      if (container) {
        container.innerHTML = ''
      }
    }
  }, [symbol, theme, mounted])

  if (!mounted) {
    return (
      <div
        className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-zinc-900 flex items-center justify-center"
        style={{ height: '300px', width: '100%' }}
      >
        <p className="text-sm text-gray-500 dark:text-gray-400">Loading chart...</p>
      </div>
    )
  }

  return (
    <div
      className="tradingview-widget-container opacity-0 animate-fade-up [animation-duration:500ms] [animation-delay:400ms] [animation-fill-mode:forwards] overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700"
      ref={containerRef}
      style={{ height: '300px', width: '100%' }}
    />
  )
}

const MemoizedTradingViewWidget = memo(TradingViewWidget)

interface StockChartProps {
  ticker: string
  theme?: 'light' | 'dark'
}

// Validate ticker format (EXCHANGE:SYMBOL)
function isValidTicker(ticker: string): boolean {
  const tickerPattern = /^(NYSE|NASDAQ|AMEX|XETR|HKEX|LSE|TSE|ASX|NSE|BSE):[A-Z0-9.]{1,5}$/
  return tickerPattern.test(ticker)
}

export function StockChart({ ticker, theme = 'light' }: StockChartProps) {
  // Validate ticker format
  if (!isValidTicker(ticker)) {
    console.warn(`Invalid ticker format: ${ticker}`)
    // Still render the widget even if validation fails - let TradingView handle it
  }

  return (
    <div className="mb-6 w-full">
      <MemoizedTradingViewWidget symbol={ticker} theme={theme} />
    </div>
  )
}
