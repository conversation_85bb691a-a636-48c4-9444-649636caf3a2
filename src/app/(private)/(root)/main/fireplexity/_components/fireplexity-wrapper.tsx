'use client'

import { useState, useEffect } from 'react'
import { useChat } from 'ai/react'
import { SearchComponent } from './search-component'
import { ChatInterface } from './chat-interface'
import { SearchResult } from '../_lib/types'
import { detectCompanyTicker } from '@/lib/fireplexity/company-ticker-map'

interface MessageData {
  sources: SearchResult[]
  followUpQuestions: string[]
  ticker?: string
}

export function FireplexityWrapper() {
  const [sources, setSources] = useState<SearchResult[]>([])
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([])
  const [searchStatus, setSearchStatus] = useState('')
  const [currentTicker, setCurrentTicker] = useState<string | null>(null)
  const [messageData, setMessageData] = useState<Map<number, MessageData>>(new Map())

  const { messages, input, handleInputChange, handleSubmit, isLoading, setInput } = useChat({
    api: '/api/fireplexity/search',
    onResponse: async (response) => {
      if (!response.body) return

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6))
                
                if (data.type === 'status') {
                  setSearchStatus(data.message)
                } else if (data.type === 'sources') {
                  setSources(data.sources)
                } else if (data.type === 'followUpQuestions') {
                  setFollowUpQuestions(data.questions)
                } else if (data.type === 'ticker') {
                  setCurrentTicker(data.ticker)
                } else if (data.type === 'messageComplete') {
                  // Store message-specific data
                  const messageIndex = messages.length // This will be the assistant message index
                  setMessageData(prev => new Map(prev).set(messageIndex, {
                    sources: data.sources || [],
                    followUpQuestions: data.followUpQuestions || [],
                    ticker: data.ticker
                  }))
                  
                  // Clear current state for next message
                  setSources([])
                  setFollowUpQuestions([])
                  setCurrentTicker(null)
                  setSearchStatus('')
                }
              } catch (e) {
                console.error('Error parsing SSE data:', e)
              }
            }
          }
        }
      } catch (error) {
        console.error('Error reading response stream:', error)
      }
    },
    onFinish: () => {
      setSearchStatus('')
    },
    onError: (error) => {
      console.error('Chat error:', error)
      setSearchStatus('')
    }
  })

  // Check environment on mount
  useEffect(() => {
    const checkEnv = async () => {
      try {
        const response = await fetch('/api/fireplexity/check-env')
        const data = await response.json()
        if (!data.hasFirecrawlKey) {
          console.warn('Firecrawl API key not found. Please add FIRECRAWL_API_KEY to your environment variables.')
        }
      } catch (error) {
        console.error('Error checking environment:', error)
      }
    }
    checkEnv()
  }, [])

  // Detect ticker from user input
  useEffect(() => {
    if (messages.length > 0) {
      const lastUserMessage = messages.filter(m => m.role === 'user').pop()
      if (lastUserMessage) {
        const ticker = detectCompanyTicker(lastUserMessage.content)
        if (ticker && !currentTicker) {
          setCurrentTicker(ticker)
        }
      }
    }
  }, [messages, currentTicker])

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return
    
    // Clear current state when starting new search
    setSources([])
    setFollowUpQuestions([])
    setSearchStatus('')
    setCurrentTicker(null)
    
    handleSubmit(e)
  }

  const handleInputChangeWrapper = (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e)
  }

  // Show search component if no messages, otherwise show chat interface
  if (messages.length === 0) {
    return (
      <div className="h-full flex flex-col">
        <SearchComponent
          input={input}
          handleInputChange={handleInputChangeWrapper}
          handleSubmit={handleFormSubmit}
          isLoading={isLoading}
        />
      </div>
    )
  }

  return (
    <div className="h-full">
      <ChatInterface
        messages={messages}
        sources={sources}
        followUpQuestions={followUpQuestions}
        searchStatus={searchStatus}
        isLoading={isLoading}
        input={input}
        handleInputChange={handleInputChangeWrapper}
        handleSubmit={handleFormSubmit}
        messageData={messageData}
        currentTicker={currentTicker}
      />
    </div>
  )
}
