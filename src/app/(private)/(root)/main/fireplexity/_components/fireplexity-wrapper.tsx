'use client'

import { useState, useEffect, useRef } from 'react'
import { useChat } from 'ai/react'
import { SearchComponent } from './search-component'
import { ChatInterface } from './chat-interface'
import { SearchResult } from '../_lib/types'
import { detectCompanyTicker } from '@/lib/fireplexity/company-ticker-map'

interface MessageData {
  sources: SearchResult[]
  followUpQuestions: string[]
  ticker?: string
}

export function FireplexityWrapper() {
  const [sources, setSources] = useState<SearchResult[]>([])
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([])
  const [searchStatus, setSearchStatus] = useState('')
  const [currentTicker, setCurrentTicker] = useState<string | null>(null)
  const [messageData, setMessageData] = useState<Map<number, MessageData>>(new Map())
  const [envError, setEnvError] = useState<string | null>(null)
  
  // Use refs to capture current state values for the complete event
  const sourcesRef = useRef(sources)
  const followUpQuestionsRef = useRef(followUpQuestions)
  const currentTickerRef = useRef(currentTicker)

  const { messages, input, handleInputChange, handleSubmit, isLoading, data } = useChat({
    api: '/api/fireplexity/search',
    onFinish: () => {
      setSearchStatus('')
    },
    onError: (error) => {
      console.error('Chat error:', error)
      setSearchStatus('')
    }
  })

  // Handle data from the AI SDK - track processed data to avoid infinite loops
  const [processedDataLength, setProcessedDataLength] = useState(0)

  useEffect(() => {
    if (data && data.length > processedDataLength) {
      // Only process new data items
      for (let i = processedDataLength; i < data.length; i++) {
        const item = data[i]

        if (!item || typeof item !== 'object' || !('type' in item)) continue

        const typedItem = item as { type: string; [key: string]: unknown }

        if (typedItem.type === 'status') {
          setSearchStatus(typedItem.message as string)
        } else if (typedItem.type === 'sources') {
          console.log('🔍 Sources received:', (typedItem.sources as SearchResult[])?.length, 'sources')
          const sourcesData = typedItem.sources as SearchResult[]
          setSources(sourcesData)
        } else if (typedItem.type === 'follow_up_questions') {
          console.log('❓ Follow-up questions received:', typedItem)
          setFollowUpQuestions(typedItem.questions as string[])
        } else if (typedItem.type === 'ticker') {
          console.log('📈 Ticker received:', typedItem)
          const tickerSymbol = typedItem.symbol as string
          console.log('📈 Setting ticker to:', tickerSymbol)
          setCurrentTicker(tickerSymbol)
        } else if (typedItem.type === 'complete') {
          console.log('✅ Complete event received:', typedItem)
          console.log('✅ Current messages length:', messages.length)
          console.log('✅ Current messages:', messages.map((m, i) => ({ index: i, role: m.role, content: m.content.substring(0, 50) + '...' })))
          console.log('✅ Sources from ref:', sourcesRef.current?.length || 0, 'sources')
          console.log('✅ Follow-up questions from ref:', followUpQuestionsRef.current?.length || 0, 'questions')
          console.log('✅ Ticker from ref:', currentTickerRef.current)
          
          // Store message-specific data using ref values (most up-to-date)
          // Since assistant messages are at odd indices (1, 3, 5, ...)
          // The complete event is triggered after the assistant message is added
          const messageIndex = messages.length - 1 // Current assistant message index
          console.log('✅ Storing data at message index:', messageIndex, 'for messages.length:', messages.length)
          console.log('✅ Assistant message at index', messageIndex, ':', messages[messageIndex]?.content?.substring(0, 50) + '...')
          
          setMessageData(prev => {
            const newMap = new Map(prev)
            newMap.set(messageIndex, {
              sources: sourcesRef.current || [],
              followUpQuestions: followUpQuestionsRef.current || [],
              ticker: currentTickerRef.current || undefined
            })
            console.log('✅ Updated messageData:', newMap)
            return newMap
          })

          // Clear current state now that it's stored in messageData
          setSearchStatus('')
          setSources([])
          setFollowUpQuestions([])
          setCurrentTicker(null)
        }
      }

      setProcessedDataLength(data.length)
    }
  }, [data, processedDataLength])

  // Check environment on mount
  useEffect(() => {
    const checkEnv = async () => {
      try {
        const response = await fetch('/api/fireplexity/check-env')
        const data = await response.json()
        if (!data.hasFirecrawlKey) {
          setEnvError('Firecrawl API key not configured. Please add FIRECRAWL_API_KEY to your environment variables.')
        }
      } catch (error) {
        console.error('Error checking environment:', error)
        setEnvError('Error checking environment configuration.')
      }
    }
    checkEnv()
  }, [])

  // Update refs when state changes
  useEffect(() => {
    sourcesRef.current = sources
  }, [sources])
  
  useEffect(() => {
    followUpQuestionsRef.current = followUpQuestions
  }, [followUpQuestions])
  
  useEffect(() => {
    currentTickerRef.current = currentTicker
  }, [currentTicker])
  
  // Debug: Log state changes
  useEffect(() => {
    console.log('🔄 State update:', {
      sourcesCount: sources.length,
      followUpQuestionsCount: followUpQuestions.length,
      currentTicker,
      searchStatus
    })
  }, [sources, followUpQuestions, currentTicker, searchStatus])

  // Detect ticker from user input
  useEffect(() => {
    if (messages.length > 0) {
      const lastUserMessage = messages.filter(m => m.role === 'user').pop()
      if (lastUserMessage) {
        const ticker = detectCompanyTicker(lastUserMessage.content)
        if (ticker && !currentTicker) {
          setCurrentTicker(ticker)
        }
      }
    }
  }, [messages, currentTicker])

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return

    // Store current data in messageData before clearing (like original Fireplexity)
    if (messages.length > 0 && (sources.length > 0 || followUpQuestions.length > 0 || currentTicker)) {
      const assistantMessages = messages.filter(m => m.role === 'assistant')
      const lastAssistantIndex = assistantMessages.length - 1
      if (lastAssistantIndex >= 0) {
        setMessageData(prev => {
          const newMap = new Map(prev)
          newMap.set(lastAssistantIndex, {
            sources: sources,
            followUpQuestions: followUpQuestions,
            ticker: currentTicker || undefined
          })
          console.log('💾 Stored message data before clearing:', newMap)
          return newMap
        })
      }
    }

    // Clear current state when starting new search
    setSources([])
    setFollowUpQuestions([])
    setSearchStatus('')
    setCurrentTicker(null)
    setProcessedDataLength(0) // Reset processed data tracking

    handleSubmit(e)
  }

  const handleInputChangeWrapper = (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e)
  }

  // Show error state if environment is not configured
  if (envError) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8">
        <div className="max-w-md text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Configuration Required</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">{envError}</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Please contact your administrator to configure the required API keys.
          </p>
        </div>
      </div>
    )
  }

  // Show search component if no messages, otherwise show chat interface
  if (messages.length === 0) {
    return (
      <div className="h-full flex flex-col">
        <SearchComponent
          input={input}
          handleInputChange={handleInputChangeWrapper}
          handleSubmit={handleFormSubmit}
          isLoading={isLoading}
        />
      </div>
    )
  }

  return (
    <div className="h-full">
      <ChatInterface
        messages={messages}
        sources={sources}
        followUpQuestions={followUpQuestions}
        searchStatus={searchStatus}
        isLoading={isLoading}
        input={input}
        handleInputChange={handleInputChangeWrapper}
        handleSubmit={handleFormSubmit}
        messageData={messageData}
        currentTicker={currentTicker}
      />
    </div>
  )
}
