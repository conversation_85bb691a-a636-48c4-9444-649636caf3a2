'use client'

import { useState, useEffect } from 'react'
import { useChat } from 'ai/react'
import { SearchComponent } from './search-component'
import { ChatInterface } from './chat-interface'
import { SearchResult } from '../_lib/types'
import { detectCompanyTicker } from '@/lib/fireplexity/company-ticker-map'

interface MessageData {
  sources: SearchResult[]
  followUpQuestions: string[]
  ticker?: string
}

export function FireplexityWrapper() {
  const [sources, setSources] = useState<SearchResult[]>([])
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([])
  const [searchStatus, setSearchStatus] = useState('')
  const [currentTicker, setCurrentTicker] = useState<string | null>(null)
  const [messageData, setMessageData] = useState<Map<number, MessageData>>(new Map())
  const [envError, setEnvError] = useState<string | null>(null)

  const { messages, input, handleInputChange, handleSubmit, isLoading, data } = useChat({
    api: '/api/fireplexity/search',
    onFinish: () => {
      setSearchStatus('')
    },
    onError: (error) => {
      console.error('Chat error:', error)
      setSearchStatus('')
    }
  })

  // Handle data from the AI SDK - track processed data to avoid infinite loops
  const [processedDataLength, setProcessedDataLength] = useState(0)

  useEffect(() => {
    if (data && data.length > processedDataLength) {
      // Only process new data items
      for (let i = processedDataLength; i < data.length; i++) {
        const item = data[i]

        if (!item || typeof item !== 'object' || !('type' in item)) continue

        const typedItem = item as { type: string; [key: string]: unknown }

        if (typedItem.type === 'status') {
          setSearchStatus(typedItem.message as string)
        } else if (typedItem.type === 'sources') {
          console.log('🔍 Sources received:', typedItem)
          const sourcesData = typedItem.sources as SearchResult[]
          console.log('🔍 Setting sources count:', sourcesData?.length)
          setSources(sourcesData)
        } else if (typedItem.type === 'follow_up_questions') {
          console.log('❓ Follow-up questions received:', typedItem)
          setFollowUpQuestions(typedItem.questions as string[])
        } else if (typedItem.type === 'ticker') {
          console.log('📈 Ticker received:', typedItem)
          const tickerSymbol = typedItem.symbol as string
          console.log('📈 Setting ticker to:', tickerSymbol)
          setCurrentTicker(tickerSymbol)
        } else if (typedItem.type === 'messageComplete') {
          // Store message-specific data
          const messageIndex = messages.length
          setMessageData(prev => new Map(prev).set(messageIndex, {
            sources: (typedItem.sources as SearchResult[]) || [],
            followUpQuestions: (typedItem.followUpQuestions as string[]) || [],
            ticker: typedItem.ticker as string
          }))

          // Don't clear current state - let it persist for the current message
          setSearchStatus('')
        }
      }

      setProcessedDataLength(data.length)
    }
  }, [data, processedDataLength, messages.length])

  // Check environment on mount
  useEffect(() => {
    const checkEnv = async () => {
      try {
        const response = await fetch('/api/fireplexity/check-env')
        const data = await response.json()
        if (!data.hasFirecrawlKey) {
          setEnvError('Firecrawl API key not configured. Please add FIRECRAWL_API_KEY to your environment variables.')
        }
      } catch (error) {
        console.error('Error checking environment:', error)
        setEnvError('Error checking environment configuration.')
      }
    }
    checkEnv()
  }, [])

  // Debug: Log state changes
  useEffect(() => {
    console.log('🔄 State update:', {
      sourcesCount: sources.length,
      followUpQuestionsCount: followUpQuestions.length,
      currentTicker,
      searchStatus
    })
  }, [sources, followUpQuestions, currentTicker, searchStatus])

  // Detect ticker from user input
  useEffect(() => {
    if (messages.length > 0) {
      const lastUserMessage = messages.filter(m => m.role === 'user').pop()
      if (lastUserMessage) {
        const ticker = detectCompanyTicker(lastUserMessage.content)
        if (ticker && !currentTicker) {
          setCurrentTicker(ticker)
        }
      }
    }
  }, [messages, currentTicker])

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return
    
    // Clear current state when starting new search
    setSources([])
    setFollowUpQuestions([])
    setSearchStatus('')
    setCurrentTicker(null)
    setProcessedDataLength(0) // Reset processed data tracking
    
    handleSubmit(e)
  }

  const handleInputChangeWrapper = (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e)
  }

  // Show error state if environment is not configured
  if (envError) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8">
        <div className="max-w-md text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Configuration Required</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">{envError}</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Please contact your administrator to configure the required API keys.
          </p>
        </div>
      </div>
    )
  }

  // Show search component if no messages, otherwise show chat interface
  if (messages.length === 0) {
    return (
      <div className="h-full flex flex-col">
        <SearchComponent
          input={input}
          handleInputChange={handleInputChangeWrapper}
          handleSubmit={handleFormSubmit}
          isLoading={isLoading}
        />
      </div>
    )
  }

  return (
    <div className="h-full">
      <ChatInterface
        messages={messages}
        sources={sources}
        followUpQuestions={followUpQuestions}
        searchStatus={searchStatus}
        isLoading={isLoading}
        input={input}
        handleInputChange={handleInputChangeWrapper}
        handleSubmit={handleFormSubmit}
        messageData={messageData}
        currentTicker={currentTicker}
      />
    </div>
  )
}
