'use client'

import { useState, useEffect, useRef } from 'react'
import { useChat } from 'ai/react'
import { SearchComponent } from './search-component'
import { ChatInterface } from './chat-interface'
import { SearchResult } from '../_lib/types'
import { detectCompanyTicker } from '@/lib/fireplexity/company-ticker-map'

interface MessageData {
  sources: SearchResult[]
  followUpQuestions: string[]
  ticker?: string
}

export function FireplexityWrapper() {
  const [sources, setSources] = useState<SearchResult[]>([])
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([])
  const [searchStatus, setSearchStatus] = useState('')
  const [currentTicker, setCurrentTicker] = useState<string | null>(null)
  const [messageData, setMessageData] = useState<Map<number, MessageData>>(new Map())
  const [envError, setEnvError] = useState<string | null>(null)

  // Use refs like the original Fireplexity
  const lastDataLength = useRef(0)
  const currentMessageIndex = useRef(0)

  const { messages, input, handleInputChange, handleSubmit, isLoading, data } = useChat({
    api: '/api/fireplexity/search',
    onResponse: () => {
      // Clear status when response starts (like original)
      setSearchStatus('')
      // Clear current data for new response (like original)
      setSources([])
      setFollowUpQuestions([])
      setCurrentTicker(null)
      // Track the current message index (assistant messages only) (like original)
      const assistantMessages = messages.filter(m => m.role === 'assistant')
      currentMessageIndex.current = assistantMessages.length
    },
    onFinish: () => {
      setSearchStatus('')
      // Reset data length tracker (like original)
      lastDataLength.current = 0
    },
    onError: (error) => {
      console.error('Chat error:', error)
      setSearchStatus('')
    }
  })

  // Handle custom data from stream - only process new items (like original)

  useEffect(() => {
    if (data && Array.isArray(data)) {
      // Only process new items that haven't been processed before
      const newItems = data.slice(lastDataLength.current)

      newItems.forEach((item) => {
        if (!item || typeof item !== 'object' || !('type' in item)) return

        const typedItem = item as unknown as { type: string; message?: string; sources?: SearchResult[]; questions?: string[]; symbol?: string }
        if (typedItem.type === 'status') {
          setSearchStatus(typedItem.message || '')
        }
        if (typedItem.type === 'ticker' && typedItem.symbol) {
          setCurrentTicker(typedItem.symbol)
          // Also store in message data map (like original)
          const newMap = new Map(messageData)
          const existingData = newMap.get(currentMessageIndex.current) || { sources: [], followUpQuestions: [] }
          newMap.set(currentMessageIndex.current, { ...existingData, ticker: typedItem.symbol })
          setMessageData(newMap)
        }
        if (typedItem.type === 'sources' && typedItem.sources) {
          setSources(typedItem.sources)
          // Also store in message data map (like original)
          const newMap = new Map(messageData)
          const existingData = newMap.get(currentMessageIndex.current) || { sources: [], followUpQuestions: [] }
          newMap.set(currentMessageIndex.current, { ...existingData, sources: typedItem.sources })
          setMessageData(newMap)
        }
        if (typedItem.type === 'follow_up_questions' && typedItem.questions) {
          setFollowUpQuestions(typedItem.questions)
          // Also store in message data map (like original)
          const newMap = new Map(messageData)
          const existingData = newMap.get(currentMessageIndex.current) || { sources: [], followUpQuestions: [] }
          newMap.set(currentMessageIndex.current, { ...existingData, followUpQuestions: typedItem.questions })
          setMessageData(newMap)
        }
      })

      // Update the last processed length (like original)
      lastDataLength.current = data.length
    }
  }, [data, messageData])

  // Check environment on mount
  useEffect(() => {
    const checkEnv = async () => {
      try {
        const response = await fetch('/api/fireplexity/check-env')
        const data = await response.json()
        if (!data.hasFirecrawlKey) {
          setEnvError('Firecrawl API key not configured. Please add FIRECRAWL_API_KEY to your environment variables.')
        }
      } catch (error) {
        console.error('Error checking environment:', error)
        setEnvError('Error checking environment configuration.')
      }
    }
    checkEnv()
  }, [])

  // Check environment on mount
  useEffect(() => {
    const checkEnv = async () => {
      try {
        const response = await fetch('/api/fireplexity/check-env')
        const data = await response.json()
        if (!data.hasFirecrawlKey) {
          setEnvError('Firecrawl API key is not configured. Please set FIRECRAWL_API_KEY in your environment variables.')
        }
      } catch (error) {
        console.error('Error checking environment:', error)
        setEnvError('Failed to check environment configuration.')
      }
    }
    checkEnv()
  }, [])

  // Detect ticker from user input
  useEffect(() => {
    if (messages.length > 0) {
      const lastUserMessage = messages.filter(m => m.role === 'user').pop()
      if (lastUserMessage) {
        const ticker = detectCompanyTicker(lastUserMessage.content)
        if (ticker && !currentTicker) {
          setCurrentTicker(ticker)
        }
      }
    }
  }, [messages, currentTicker])

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return

    // Store current data in messageData before clearing (like original Fireplexity)
    if (messages.length > 0 && (sources.length > 0 || followUpQuestions.length > 0 || currentTicker)) {
      const assistantMessages = messages.filter(m => m.role === 'assistant')
      const lastAssistantIndex = assistantMessages.length - 1
      if (lastAssistantIndex >= 0) {
        setMessageData(prev => {
          const newMap = new Map(prev)
          newMap.set(lastAssistantIndex, {
            sources: sources,
            followUpQuestions: followUpQuestions,
            ticker: currentTicker || undefined
          })
          console.log('💾 Stored message data before clearing:', newMap)
          return newMap
        })
      }
    }

    // Clear current state when starting new search
    setSources([])
    setFollowUpQuestions([])
    setSearchStatus('')
    setCurrentTicker(null)

    handleSubmit(e)
  }

  const handleInputChangeWrapper = (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e)
  }

  // Show error state if environment is not configured
  if (envError) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8">
        <div className="max-w-md text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Configuration Required</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">{envError}</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Please contact your administrator to configure the required API keys.
          </p>
        </div>
      </div>
    )
  }

  // Show search component if no messages, otherwise show chat interface
  if (messages.length === 0) {
    return (
      <div className="h-full flex flex-col">
        <SearchComponent
          input={input}
          handleInputChange={handleInputChangeWrapper}
          handleSubmit={handleFormSubmit}
          isLoading={isLoading}
        />
      </div>
    )
  }

  return (
    <div className="h-full">
      <ChatInterface
        messages={messages}
        sources={sources}
        followUpQuestions={followUpQuestions}
        searchStatus={searchStatus}
        isLoading={isLoading}
        input={input}
        handleInputChange={handleInputChangeWrapper}
        handleSubmit={handleFormSubmit}
        messageData={messageData}
        currentTicker={currentTicker}
      />
    </div>
  )
}
