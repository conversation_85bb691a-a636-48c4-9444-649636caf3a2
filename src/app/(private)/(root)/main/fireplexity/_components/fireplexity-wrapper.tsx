'use client'

import { useState, useEffect } from 'react'
import { useChat } from 'ai/react'
import { SearchComponent } from './search-component'
import { ChatInterface } from './chat-interface'
import { SearchResult } from '../_lib/types'
import { detectCompanyTicker } from '@/lib/fireplexity/company-ticker-map'

interface MessageData {
  sources: SearchResult[]
  followUpQuestions: string[]
  ticker?: string
}

export function FireplexityWrapper() {
  const [sources, setSources] = useState<SearchResult[]>([])
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([])
  const [searchStatus, setSearchStatus] = useState('')
  const [currentTicker, setCurrentTicker] = useState<string | null>(null)
  const [messageData, setMessageData] = useState<Map<number, MessageData>>(new Map())
  const [envError, setEnvError] = useState<string | null>(null)

  const { messages, input, handleInputChange, handleSubmit, isLoading, setInput, data } = useChat({
    api: '/api/fireplexity/search',
    onFinish: () => {
      setSearchStatus('')
    },
    onError: (error) => {
      console.error('Chat error:', error)
      setSearchStatus('')
    }
  })

  // Handle data from the AI SDK
  useEffect(() => {
    if (data && data.length > 0) {
      const latestData = data[data.length - 1]

      if (latestData.type === 'status') {
        setSearchStatus(latestData.message)
      } else if (latestData.type === 'sources') {
        setSources(latestData.sources)
      } else if (latestData.type === 'followUpQuestions') {
        setFollowUpQuestions(latestData.questions)
      } else if (latestData.type === 'ticker') {
        setCurrentTicker(latestData.ticker)
      } else if (latestData.type === 'messageComplete') {
        // Store message-specific data
        const messageIndex = messages.length
        setMessageData(prev => new Map(prev).set(messageIndex, {
          sources: latestData.sources || [],
          followUpQuestions: latestData.followUpQuestions || [],
          ticker: latestData.ticker
        }))

        // Clear current state for next message
        setSources([])
        setFollowUpQuestions([])
        setCurrentTicker(null)
        setSearchStatus('')
      }
    }
  }, [data, messages.length])

  // Check environment on mount
  useEffect(() => {
    const checkEnv = async () => {
      try {
        const response = await fetch('/api/fireplexity/check-env')
        const data = await response.json()
        if (!data.hasFirecrawlKey) {
          setEnvError('Firecrawl API key not configured. Please add FIRECRAWL_API_KEY to your environment variables.')
        }
      } catch (error) {
        console.error('Error checking environment:', error)
        setEnvError('Error checking environment configuration.')
      }
    }
    checkEnv()
  }, [])

  // Detect ticker from user input
  useEffect(() => {
    if (messages.length > 0) {
      const lastUserMessage = messages.filter(m => m.role === 'user').pop()
      if (lastUserMessage) {
        const ticker = detectCompanyTicker(lastUserMessage.content)
        if (ticker && !currentTicker) {
          setCurrentTicker(ticker)
        }
      }
    }
  }, [messages, currentTicker])

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return
    
    // Clear current state when starting new search
    setSources([])
    setFollowUpQuestions([])
    setSearchStatus('')
    setCurrentTicker(null)
    
    handleSubmit(e)
  }

  const handleInputChangeWrapper = (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e)
  }

  // Show error state if environment is not configured
  if (envError) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8">
        <div className="max-w-md text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Configuration Required</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">{envError}</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Please contact your administrator to configure the required API keys.
          </p>
        </div>
      </div>
    )
  }

  // Show search component if no messages, otherwise show chat interface
  if (messages.length === 0) {
    return (
      <div className="h-full flex flex-col">
        <SearchComponent
          input={input}
          handleInputChange={handleInputChangeWrapper}
          handleSubmit={handleFormSubmit}
          isLoading={isLoading}
        />
      </div>
    )
  }

  return (
    <div className="h-full">
      <ChatInterface
        messages={messages}
        sources={sources}
        followUpQuestions={followUpQuestions}
        searchStatus={searchStatus}
        isLoading={isLoading}
        input={input}
        handleInputChange={handleInputChangeWrapper}
        handleSubmit={handleFormSubmit}
        messageData={messageData}
        currentTicker={currentTicker}
      />
    </div>
  )
}
